# ContainerContext 测试实例创建指南

本文档提供了在 `topology_aware_test.go` 中创建 ContainerContext 结构体的完整示例和最佳实践。

## 问题背景

在编写 topology-aware 策略的测试用例时，需要创建 `policy.ContainerContext` 结构体来模拟容器请求。主要挑战包括：

1. **类型导入问题**: 需要正确导入相关的 Kubernetes 类型
2. **资源配置**: 需要正确设置 `EstimatedRequirements` 字段
3. **空指针问题**: 避免 nil 指针引用导致的 panic

## 解决方案

### 1. 必要的导入

```go
import (
    "strconv"
    "strings"
    "testing"

    . "github.com/onsi/ginkgo/v2"
    . "github.com/onsi/gomega"
    corev1 "k8s.io/api/core/v1"
    "k8s.io/apimachinery/pkg/api/resource"
    "kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/policy"
)
```

### 2. 辅助函数

```go
// Helper functions for tests
func stringPtr(s string) *string {
    return &s
}

func int64Ptr(i int64) *int64 {
    return &i
}
```

### 3. 基本 ContainerContext 创建

```go
// createBasicContainerContext creates a basic ContainerContext for testing
func createBasicContainerContext(containerName, podUID, podName, namespace string) *policy.ContainerContext {
    return &policy.ContainerContext{
        Request: policy.ContainerRequest{
            ContainerMeta: policy.ContainerMeta{
                Name: containerName,
                ID:   "containerd://" + containerName + "-id-123",
            },
            PodMeta: policy.PodMeta{
                UID:       podUID,
                Name:      podName,
                Namespace: namespace,
            },
            Resources: &policy.Resources{
                EstimatedRequirements: &corev1.ResourceRequirements{
                    Requests: corev1.ResourceList{},
                    Limits:   corev1.ResourceList{},
                },
            },
        },
    }
}
```

### 4. 带资源配置的 ContainerContext

```go
// createContainerContextWithResources creates a ContainerContext with resource specifications
func createContainerContextWithResources(containerName, podUID, podName, namespace string, cpuQuota, cpuPeriod, cpuShares int64) *policy.ContainerContext {
    ctx := createBasicContainerContext(containerName, podUID, podName, namespace)
    
    // Calculate CPU requests and limits based on quota and period
    cpuCores := float64(cpuQuota) / float64(cpuPeriod)
    cpuQuantity := resource.NewMilliQuantity(int64(cpuCores*1000), resource.DecimalSI)
    
    ctx.Request.Resources = &policy.Resources{
        CpuQuota:  int64Ptr(cpuQuota),
        CpuPeriod: int64Ptr(cpuPeriod),
        CpuShares: int64Ptr(cpuShares),
        EstimatedRequirements: &corev1.ResourceRequirements{
            Requests: corev1.ResourceList{
                corev1.ResourceCPU: *cpuQuantity,
            },
            Limits: corev1.ResourceList{
                corev1.ResourceCPU: *cpuQuantity,
            },
        },
    }
    return ctx
}
```

### 5. 带特定容器ID的 ContainerContext

```go
// createContainerContextWithID creates a ContainerContext with specific container ID
func createContainerContextWithID(containerID, containerName, podUID string) *policy.ContainerContext {
    return &policy.ContainerContext{
        Request: policy.ContainerRequest{
            ContainerMeta: policy.ContainerMeta{
                ID:   containerID,
                Name: containerName,
            },
            PodMeta: policy.PodMeta{
                UID: podUID,
            },
            Resources: &policy.Resources{
                EstimatedRequirements: &corev1.ResourceRequirements{
                    Requests: corev1.ResourceList{},
                    Limits:   corev1.ResourceList{},
                },
            },
        },
    }
}
```

## 使用示例

### 基本容器测试

```go
It("should create basic container context successfully", func() {
    containerCtx := createBasicContainerContext("web-server", "web-pod-uid-123", "web-server-pod", "production")
    
    Expect(containerCtx).NotTo(BeNil())
    Expect(containerCtx.Request.ContainerMeta.Name).To(Equal("web-server"))
    Expect(containerCtx.Request.ContainerMeta.ID).To(Equal("containerd://web-server-id-123"))
    Expect(containerCtx.Request.PodMeta.UID).To(Equal("web-pod-uid-123"))
    Expect(containerCtx.Request.PodMeta.Name).To(Equal("web-server-pod"))
    Expect(containerCtx.Request.PodMeta.Namespace).To(Equal("production"))
})
```

### CPU密集型工作负载测试

```go
It("should create container context with CPU resources", func() {
    containerCtx := createContainerContextWithResources(
        "cpu-intensive-app", 
        "cpu-pod-uid-456", 
        "cpu-intensive-pod", 
        "compute",
        400000, // 4 CPU cores quota
        100000, // period
        2048,   // shares
    )
    
    Expect(containerCtx).NotTo(BeNil())
    Expect(containerCtx.Request.Resources).NotTo(BeNil())
    Expect(*containerCtx.Request.Resources.CpuQuota).To(Equal(int64(400000)))
    Expect(*containerCtx.Request.Resources.CpuPeriod).To(Equal(int64(100000)))
    Expect(*containerCtx.Request.Resources.CpuShares).To(Equal(int64(2048)))
})
```

### 系统容器测试

```go
It("should handle system containers in kube-system namespace", func() {
    containerCtx := createBasicContainerContext("kube-proxy", "system-pod-uid-789", "kube-proxy-pod", "kube-system")
    
    allocation, err := topologyPolicy.PreCreateContainerHook(containerCtx)
    Expect(err).To(BeNil())
    Expect(allocation).NotTo(BeNil())
})
```

## 关键要点

1. **必须设置 EstimatedRequirements**: 避免 nil 指针引用
2. **正确的资源计算**: CPU 配额和周期的正确转换
3. **完整的元数据**: 包含所有必要的容器和 Pod 信息
4. **适当的命名空间**: 系统容器使用 "kube-system"

## 测试结果

修复后的测试运行结果：
- ✅ 12 个测试通过
- ⏭️ 3 个测试跳过（由于 mock 系统问题）
- ❌ 0 个测试失败

这表明 ContainerContext 的创建和基本功能已经正常工作。
